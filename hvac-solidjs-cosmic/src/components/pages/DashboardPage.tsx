import { type Component, createSignal, onMount } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { 
  BarChart3, 
  Users, 
  Wrench, 
  Package, 
  TrendingUp,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Activity
} from 'lucide-solid'

export const DashboardPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [selectedPeriod, setSelectedPeriod] = createSignal('7d')

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  const kpis = [
    {
      title: 'Total Revenue',
      value: '$137,618',
      change: '+23.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'golden'
    },
    {
      title: 'Active Customers',
      value: '1,337',
      change: '+12.3%',
      trend: 'up',
      icon: Users,
      color: 'cosmic'
    },
    {
      title: 'Service Orders',
      value: '618',
      change: '+8.7%',
      trend: 'up',
      icon: Wrench,
      color: 'divine'
    },
    {
      title: 'Inventory Value',
      value: '$89,144',
      change: '-2.1%',
      trend: 'down',
      icon: Package,
      color: 'cosmic'
    }
  ]

  const recentOrders = [
    {
      id: 'SO-001',
      customer: 'Acme Corp',
      service: 'AC Installation',
      status: 'in-progress',
      priority: 'high',
      date: '2024-01-15'
    },
    {
      id: 'SO-002',
      customer: 'Tech Solutions',
      service: 'HVAC Maintenance',
      status: 'completed',
      priority: 'medium',
      date: '2024-01-14'
    },
    {
      id: 'SO-003',
      customer: 'Global Industries',
      service: 'System Repair',
      status: 'pending',
      priority: 'urgent',
      date: '2024-01-13'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircle
      case 'in-progress':
        return Clock
      case 'pending':
        return AlertTriangle
      default:
        return Clock
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400'
      case 'in-progress':
        return 'text-blue-400'
      case 'pending':
        return 'text-yellow-400'
      default:
        return 'text-gray-400'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div class="p-golden-lg space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div>
          <h1 class="text-3xl font-bold text-white mb-golden-sm">
            Dashboard Overview
          </h1>
          <p class="text-white/70">
            Monitor your HVAC business performance in real-time
          </p>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          <select
            class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
            value={selectedPeriod()}
            onChange={(e) => setSelectedPeriod(e.target.value)}
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <GoldenButton variant="cosmic" size="md" glow>
            <Activity size={16} class="mr-golden-xs" />
            Refresh
          </GoldenButton>
        </div>
      </div>

      {/* KPI Cards */}
      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-golden-md transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {kpis.map((kpi, index) => {
          const Icon = kpi.icon
          return (
            <CosmicCard variant="glass" size="md" glow hover3d>
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <p class="text-white/70 text-sm mb-golden-xs">{kpi.title}</p>
                  <p class="text-2xl font-bold text-white mb-golden-xs">{kpi.value}</p>
                  <div class="flex items-center space-x-golden-xs">
                    <TrendingUp 
                      size={14} 
                      class={kpi.trend === 'up' ? 'text-green-400' : 'text-red-400'} 
                    />
                    <span 
                      class={`text-sm ${kpi.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}
                    >
                      {kpi.change}
                    </span>
                  </div>
                </div>
                <div class={`w-12 h-12 bg-gradient-to-r ${
                  kpi.color === 'cosmic' ? 'from-cosmic-400 to-cosmic-600' :
                  kpi.color === 'golden' ? 'from-golden-400 to-golden-600' :
                  'from-divine-400 to-divine-600'
                } rounded-lg flex items-center justify-center`}>
                  <Icon size={24} class="text-white" />
                </div>
              </div>
            </CosmicCard>
          )
        })}
      </div>

      {/* Charts and Recent Activity */}
      <div
        class={`grid grid-cols-1 lg:grid-cols-3 gap-golden-lg transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        {/* Revenue Chart */}
        <div class="lg:col-span-2">
          <CosmicCard variant="glass" size="lg" glow>
            <div class="flex items-center justify-between mb-golden-md">
              <h3 class="text-xl font-bold text-white">Revenue Trend</h3>
              <div class="flex items-center space-x-golden-sm">
                <div class="w-3 h-3 bg-cosmic-400 rounded-full"></div>
                <span class="text-white/70 text-sm">Revenue</span>
              </div>
            </div>
            <div class="h-64 bg-white/5 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <BarChart3 size={48} class="text-white/30 mx-auto mb-golden-sm" />
                <p class="text-white/50">Chart visualization coming soon</p>
                <p class="text-white/30 text-sm">Powered by cosmic algorithms</p>
              </div>
            </div>
          </CosmicCard>
        </div>

        {/* Recent Service Orders */}
        <div>
          <CosmicCard variant="glass" size="lg" glow>
            <h3 class="text-xl font-bold text-white mb-golden-md">Recent Orders</h3>
            <div class="space-y-golden-sm">
              {recentOrders.map((order) => {
                const StatusIcon = getStatusIcon(order.status)
                return (
                  <div class="bg-white/5 rounded-lg p-golden-sm border border-white/10 hover:border-white/20 transition-colors">
                    <div class="flex items-start justify-between mb-golden-xs">
                      <div class="flex items-center space-x-golden-sm">
                        <StatusIcon size={16} class={getStatusColor(order.status)} />
                        <span class="text-white font-medium text-sm">{order.id}</span>
                      </div>
                      <div class={`w-2 h-2 rounded-full ${getPriorityColor(order.priority)}`}></div>
                    </div>
                    <p class="text-white/80 text-sm mb-golden-xs">{order.customer}</p>
                    <p class="text-white/60 text-xs mb-golden-xs">{order.service}</p>
                    <div class="flex items-center justify-between">
                      <span class="text-white/50 text-xs">{order.date}</span>
                      <span class={`text-xs px-golden-xs py-0.5 rounded ${getStatusColor(order.status)} bg-current/10`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
            <div class="mt-golden-md">
              <GoldenButton variant="cosmic" size="sm" class="w-full">
                View All Orders
              </GoldenButton>
            </div>
          </CosmicCard>
        </div>
      </div>

      {/* Quick Actions */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <h3 class="text-xl font-bold text-white mb-golden-md">Quick Actions</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-md">
            <GoldenButton variant="cosmic" size="lg" glow physics class="h-16">
              <Users size={20} class="mr-golden-sm" />
              Add Customer
            </GoldenButton>
            <GoldenButton variant="golden" size="lg" glow physics class="h-16">
              <Wrench size={20} class="mr-golden-sm" />
              Create Service Order
            </GoldenButton>
            <GoldenButton variant="divine" size="lg" glow physics class="h-16">
              <Package size={20} class="mr-golden-sm" />
              Manage Inventory
            </GoldenButton>
          </div>
        </CosmicCard>
      </div>
    </div>
  )
}
