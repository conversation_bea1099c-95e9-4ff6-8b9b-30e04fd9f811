import { type Component, createSignal, onMount, For } from 'solid-js'
import { CosmicCard } from '../atoms/CosmicCard'
import { GoldenButton } from '../atoms/GoldenButton'
import { 
  Users, 
  Search, 
  Plus, 
  Mail, 
  Phone, 
  MapPin,
  Edit,
  Trash2,
  Eye,
  Filter,
  Download
} from 'lucide-solid'

interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address: string
  city: string
  status: 'active' | 'inactive' | 'pending'
  totalOrders: number
  totalValue: number
  lastContact: string
}

export const CustomersPage: Component = () => {
  const [isLoaded, setIsLoaded] = createSignal(false)
  const [searchTerm, setSearchTerm] = createSignal('')
  const [selectedStatus, setSelectedStatus] = createSignal('all')
  const [showAddModal, setShowAddModal] = createSignal(false)

  onMount(() => {
    setTimeout(() => setIsLoaded(true), 200)
  })

  // Mock customer data
  const [customers] = createSignal<Customer[]>([
    {
      id: 'CUST-001',
      name: 'Acme Corporation',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Business Ave',
      city: 'New York, NY',
      status: 'active',
      totalOrders: 15,
      totalValue: 45000,
      lastContact: '2024-01-15'
    },
    {
      id: 'CUST-002',
      name: 'Tech Solutions Inc',
      email: '<EMAIL>',
      phone: '+****************',
      address: '456 Innovation Blvd',
      city: 'San Francisco, CA',
      status: 'active',
      totalOrders: 8,
      totalValue: 28000,
      lastContact: '2024-01-14'
    },
    {
      id: 'CUST-003',
      name: 'Global Industries',
      email: '<EMAIL>',
      phone: '+****************',
      address: '789 Corporate Dr',
      city: 'Chicago, IL',
      status: 'pending',
      totalOrders: 3,
      totalValue: 12000,
      lastContact: '2024-01-10'
    },
    {
      id: 'CUST-004',
      name: 'Local Business LLC',
      email: '<EMAIL>',
      phone: '+****************',
      address: '321 Main Street',
      city: 'Austin, TX',
      status: 'inactive',
      totalOrders: 22,
      totalValue: 67000,
      lastContact: '2023-12-20'
    }
  ])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500'
      case 'pending':
        return 'bg-yellow-500'
      case 'inactive':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
  }

  const filteredCustomers = () => {
    return customers().filter(customer => {
      const matchesSearch = customer.name.toLowerCase().includes(searchTerm().toLowerCase()) ||
                           customer.email.toLowerCase().includes(searchTerm().toLowerCase())
      const matchesStatus = selectedStatus() === 'all' || customer.status === selectedStatus()
      return matchesSearch && matchesStatus
    })
  }

  return (
    <div class="p-golden-lg space-y-golden-lg">
      {/* Header */}
      <div
        class={`flex flex-col lg:flex-row lg:items-center lg:justify-between transition-all duration-1000 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <div class="flex items-center space-x-golden-md">
          <div class="w-12 h-12 bg-gradient-to-r from-cosmic-400 to-divine-400 rounded-lg flex items-center justify-center">
            <Users size={24} class="text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-white">Customers</h1>
            <p class="text-white/70">Manage your customer relationships</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-golden-sm mt-golden-md lg:mt-0">
          <GoldenButton variant="cosmic" size="md" glow>
            <Download size={16} class="mr-golden-xs" />
            Export
          </GoldenButton>
          <GoldenButton variant="golden" size="md" glow onClick={() => setShowAddModal(true)}>
            <Plus size={16} class="mr-golden-xs" />
            Add Customer
          </GoldenButton>
        </div>
      </div>

      {/* Filters and Search */}
      <div
        class={`transition-all duration-1000 delay-200 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="md" glow>
          <div class="flex flex-col lg:flex-row lg:items-center space-y-golden-md lg:space-y-0 lg:space-x-golden-md">
            {/* Search */}
            <div class="flex-1 relative">
              <Search size={20} class="absolute left-golden-sm top-1/2 transform -translate-y-1/2 text-white/50" />
              <input
                type="text"
                placeholder="Search customers..."
                class="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg pl-10 pr-golden-md py-golden-sm text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={searchTerm()}
                onInput={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <div class="flex items-center space-x-golden-sm">
              <Filter size={16} class="text-white/70" />
              <select
                class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg px-golden-md py-golden-sm text-white focus:outline-none focus:ring-2 focus:ring-cosmic-400"
                value={selectedStatus()}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CosmicCard>
      </div>

      {/* Customer Stats */}
      <div
        class={`grid grid-cols-1 md:grid-cols-3 gap-golden-md transition-all duration-1000 delay-400 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="cosmic" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {customers().filter(c => c.status === 'active').length}
            </div>
            <div class="text-white/70 text-sm">Active Customers</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="golden" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">
              ${customers().reduce((sum, c) => sum + c.totalValue, 0).toLocaleString()}
            </div>
            <div class="text-white/70 text-sm">Total Value</div>
          </div>
        </CosmicCard>
        <CosmicCard variant="divine" size="md" glow>
          <div class="text-center">
            <div class="text-2xl font-bold text-white mb-golden-xs">
              {customers().reduce((sum, c) => sum + c.totalOrders, 0)}
            </div>
            <div class="text-white/70 text-sm">Total Orders</div>
          </div>
        </CosmicCard>
      </div>

      {/* Customer List */}
      <div
        class={`transition-all duration-1000 delay-600 ${
          isLoaded() ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <CosmicCard variant="glass" size="lg" glow>
          <div class="space-y-golden-md">
            <For each={filteredCustomers()}>
              {(customer) => (
                <div class="bg-white/5 rounded-lg p-golden-md border border-white/10 hover:border-white/20 transition-all duration-300 hover:bg-white/10">
                  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-golden-sm lg:space-y-0">
                    {/* Customer Info */}
                    <div class="flex-1">
                      <div class="flex items-center space-x-golden-md mb-golden-sm">
                        <div>
                          <h3 class="text-lg font-bold text-white">{customer.name}</h3>
                          <p class="text-white/70 text-sm">{customer.id}</p>
                        </div>
                        <div class={`w-3 h-3 rounded-full ${getStatusColor(customer.status)}`}></div>
                        <span class="text-white/70 text-sm">{getStatusText(customer.status)}</span>
                      </div>
                      
                      <div class="grid grid-cols-1 md:grid-cols-3 gap-golden-sm text-sm">
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <Mail size={14} />
                          <span>{customer.email}</span>
                        </div>
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <Phone size={14} />
                          <span>{customer.phone}</span>
                        </div>
                        <div class="flex items-center space-x-golden-xs text-white/70">
                          <MapPin size={14} />
                          <span>{customer.city}</span>
                        </div>
                      </div>
                    </div>

                    {/* Stats */}
                    <div class="flex items-center space-x-golden-lg">
                      <div class="text-center">
                        <div class="text-lg font-bold text-white">{customer.totalOrders}</div>
                        <div class="text-white/70 text-xs">Orders</div>
                      </div>
                      <div class="text-center">
                        <div class="text-lg font-bold text-white">${customer.totalValue.toLocaleString()}</div>
                        <div class="text-white/70 text-xs">Value</div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div class="flex items-center space-x-golden-xs">
                      <GoldenButton variant="cosmic" size="sm" glow>
                        <Eye size={14} />
                      </GoldenButton>
                      <GoldenButton variant="golden" size="sm" glow>
                        <Edit size={14} />
                      </GoldenButton>
                      <GoldenButton variant="divine" size="sm" glow>
                        <Trash2 size={14} />
                      </GoldenButton>
                    </div>
                  </div>
                </div>
              )}
            </For>
          </div>
        </CosmicCard>
      </div>
    </div>
  )
}
