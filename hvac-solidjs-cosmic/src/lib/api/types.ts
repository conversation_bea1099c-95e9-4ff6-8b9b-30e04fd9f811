// 🌌 COSMIC API TYPES - Perfect Type Safety for HVAC CRM
// Generated from GoBackend-Kratos tRPC definitions

// 🏢 Customer Management Types
export interface Customer {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  company?: string
  status: 'active' | 'inactive' | 'prospect'
  source?: string
  tags?: string[]
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateCustomerInput {
  name: string
  email: string
  phone?: string
  address?: string
  company?: string
  source?: string
  tags?: string[]
  notes?: string
}

export interface UpdateCustomerInput {
  id: string
  name?: string
  email?: string
  phone?: string
  address?: string
  company?: string
  status?: 'active' | 'inactive' | 'prospect'
  tags?: string[]
  notes?: string
}

// 🔧 Service Job Types
export interface ServiceJob {
  id: string
  customerId: string
  title: string
  description: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate?: string
  completedDate?: string
  assignedTechnician?: string
  estimatedDuration?: number
  actualDuration?: number
  cost?: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CreateJobInput {
  customerId: string
  title: string
  description: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate?: string
  assignedTechnician?: string
  estimatedDuration?: number
  cost?: number
  notes?: string
}

// 📊 Analytics Types
export interface DashboardMetrics {
  totalRevenue: number
  revenueGrowth: number
  totalCustomers: number
  customerGrowth: number
  activeJobs: number
  jobsGrowth: number
  inventoryValue: number
  inventoryGrowth: number
}

export interface RevenueData {
  date: string
  revenue: number
  jobs: number
}

// 🧠 AI Analysis Types
export interface AIAnalysisResult {
  sentiment: 'positive' | 'neutral' | 'negative'
  confidence: number
  keywords: string[]
  summary: string
  actionItems?: string[]
  priority?: 'low' | 'medium' | 'high'
}

export interface EmailAnalysis {
  id: string
  emailId: string
  customerId?: string
  analysis: AIAnalysisResult
  processedAt: string
}

// 📧 Email Types
export interface EmailMessage {
  id: string
  from: string
  to: string
  subject: string
  body: string
  receivedAt: string
  processed: boolean
  customerId?: string
  analysis?: AIAnalysisResult
}

// 🔄 Workflow Types
export interface WorkflowStep {
  id: string
  name: string
  description: string
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  assignedTo?: string
  dueDate?: string
  completedAt?: string
}

export interface Workflow {
  id: string
  name: string
  description: string
  status: 'active' | 'paused' | 'completed'
  steps: WorkflowStep[]
  createdAt: string
  updatedAt: string
}

// 🔍 Search and Filter Types
export interface SearchFilters {
  query?: string
  status?: string[]
  dateFrom?: string
  dateTo?: string
  tags?: string[]
  assignedTo?: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 🚀 API Response Types
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    timestamp: string
    requestId: string
  }
}

// 🌟 tRPC Router Type Definition
import type { AnyRouter, ProcedureRecord } from '@trpc/server';

export interface AppRouter extends AnyRouter {
  _def: {
    router: true;
    procedures: {
      customer: ProcedureRecord;
      job: ProcedureRecord;
      analytics: ProcedureRecord;
      ai: ProcedureRecord;
      email: ProcedureRecord;
      workflow: ProcedureRecord;
    };
  };

  // Customer procedures
  customer: {
    list: {
      query: (input: { filters?: SearchFilters; pagination?: PaginationParams }) => Promise<PaginatedResponse<Customer>>
    }
    get: {
      query: (input: { id: string }) => Promise<Customer>
    }
    create: {
      mutate: (input: CreateCustomerInput) => Promise<Customer>
    }
    update: {
      mutate: (input: UpdateCustomerInput) => Promise<Customer>
    }
    delete: {
      mutate: (input: { id: string }) => Promise<{ success: boolean }>
    }
  }

  // Job procedures
  job: {
    list: {
      query: (input: { filters?: SearchFilters; pagination?: PaginationParams }) => Promise<PaginatedResponse<ServiceJob>>
    }
    get: {
      query: (input: { id: string }) => Promise<ServiceJob>
    }
    create: {
      mutate: (input: CreateJobInput) => Promise<ServiceJob>
    }
    update: {
      mutate: (input: Partial<ServiceJob> & { id: string }) => Promise<ServiceJob>
    }
    delete: {
      mutate: (input: { id: string }) => Promise<{ success: boolean }>
    }
  }

  // Analytics procedures
  analytics: {
    dashboard: {
      query: () => Promise<DashboardMetrics>
    }
    revenue: {
      query: (input: { dateFrom: string; dateTo: string }) => Promise<RevenueData[]>
    }
  }

  // AI procedures
  ai: {
    analyzeEmail: {
      mutate: (input: { emailId: string }) => Promise<EmailAnalysis>
    }
    analyzeText: {
      mutate: (input: { text: string; context?: string }) => Promise<AIAnalysisResult>
    }
  }

  // Email procedures
  email: {
    list: {
      query: (input: { filters?: SearchFilters; pagination?: PaginationParams }) => Promise<PaginatedResponse<EmailMessage>>
    }
    process: {
      mutate: (input: { emailId: string }) => Promise<EmailAnalysis>
    }
  }

  // Workflow procedures
  workflow: {
    list: {
      query: () => Promise<Workflow[]>
    }
    get: {
      query: (input: { id: string }) => Promise<Workflow>
    }
    create: {
      mutate: (input: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Workflow>
    }
    updateStep: {
      mutate: (input: { workflowId: string; stepId: string; status: WorkflowStep['status'] }) => Promise<WorkflowStep>
    }
  }
}

// 🎯 Export all types for cosmic integration
