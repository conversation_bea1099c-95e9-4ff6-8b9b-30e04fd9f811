import { type Component } from 'solid-js'
import { Router, Route } from '@solidjs/router'
import { QueryClient, QueryClientProvider } from '@tanstack/solid-query'

// Layout Components
import { MainLayout } from './components/templates/MainLayout'

// Page Components
import { HomePage } from './components/pages/HomePage'
import { DashboardPage } from './components/pages/DashboardPage'
import { CustomersPage } from './components/pages/CustomersPage'
import { ServiceOrdersPage } from './components/pages/ServiceOrdersPage'
import { InventoryPage } from './components/pages/InventoryPage'
import { AnalyticsPage } from './components/pages/AnalyticsPage'
import { SettingsPage } from './components/pages/SettingsPage'

// Global Components
import { ToastContainer, toast } from './components/molecules/CosmicToast'

// Create QueryClient for data management
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes
    },
  },
})

const App: Component = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <Route path="/" component={MainLayout}>
          <Route path="/" component={HomePage} />
          <Route path="/dashboard" component={DashboardPage} />
          <Route path="/customers" component={CustomersPage} />
          <Route path="/service-orders" component={ServiceOrdersPage} />
          <Route path="/inventory" component={InventoryPage} />
          <Route path="/analytics" component={AnalyticsPage} />
          <Route path="/settings" component={SettingsPage} />
        </Route>
      </Router>

      {/* Global Toast Container */}
      <ToastContainer toasts={toast.getToasts()} position="top-right" />
    </QueryClientProvider>
  )
}

export default App
